# AI智能清洗策略系统 - 前端Dockerfile
# 多阶段构建：构建阶段
FROM node:16-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY frontend-react-stable/package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY frontend-react-stable/ ./

# 构建应用
RUN npm run build

# 生产阶段
FROM nginx:alpine

# 复制自定义nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 复制构建产物
COPY --from=builder /app/build /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
