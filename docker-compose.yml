version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: ai-traffic-backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - ENV=production
    volumes:
      - ./data:/app/data
      - ./models:/app/models
      - ./logs:/app/logs
      - ./task_storage.json:/app/task_storage.json
      - ./task_results.json:/app/task_results.json
      - ./users.json:/app/users.json
      - ./model_registry.json:/app/model_registry.json
    networks:
      - ai-traffic-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端服务
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: ai-traffic-frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    networks:
      - ai-traffic-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis (可选，用于缓存和会话存储)
  redis:
    image: redis:7-alpine
    container_name: ai-traffic-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-traffic-network
    command: redis-server --appendonly yes

networks:
  ai-traffic-network:
    driver: bridge

volumes:
  redis_data:
    driver: local
