#!/bin/bash

# AI智能清洗策略系统 - 自动化部署脚本
# 使用方法: ./deploy.sh [环境] [操作]
# 环境: dev, staging, prod
# 操作: build, start, stop, restart, logs, clean

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
PROJECT_NAME="ai-traffic-clean"
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"

# 函数定义
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 环境检查
check_environment() {
    log_info "检查环境配置..."
    
    if [ ! -f "$ENV_FILE" ]; then
        log_warning ".env 文件不存在，从示例文件创建..."
        cp .env.example .env
        log_warning "请编辑 .env 文件配置相关参数"
    fi
    
    # 创建必要的目录
    mkdir -p data/input data/output models logs
    
    log_success "环境检查完成"
}

# 构建镜像
build_images() {
    log_info "构建 Docker 镜像..."
    
    docker-compose -f $COMPOSE_FILE build --no-cache
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    docker-compose -f $COMPOSE_FILE up -d
    
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose -f $COMPOSE_FILE ps | grep -q "Up"; then
        log_success "服务启动成功"
        show_status
    else
        log_error "服务启动失败"
        docker-compose -f $COMPOSE_FILE logs
        exit 1
    fi
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    docker-compose -f $COMPOSE_FILE down
    
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    
    stop_services
    start_services
}

# 显示日志
show_logs() {
    local service=$1
    
    if [ -z "$service" ]; then
        docker-compose -f $COMPOSE_FILE logs -f
    else
        docker-compose -f $COMPOSE_FILE logs -f $service
    fi
}

# 显示状态
show_status() {
    log_info "服务状态:"
    docker-compose -f $COMPOSE_FILE ps
    
    echo ""
    log_info "访问地址:"
    echo "前端: http://localhost"
    echo "后端API: http://localhost:8000"
    echo "API文档: http://localhost:8000/docs"
}

# 清理
clean_up() {
    log_warning "清理 Docker 资源..."
    
    docker-compose -f $COMPOSE_FILE down -v --remove-orphans
    docker system prune -f
    
    log_success "清理完成"
}

# 备份数据
backup_data() {
    local backup_dir="backup/$(date +%Y%m%d_%H%M%S)"
    
    log_info "备份数据到 $backup_dir..."
    
    mkdir -p $backup_dir
    cp -r data/ $backup_dir/
    cp *.json $backup_dir/ 2>/dev/null || true
    
    log_success "数据备份完成"
}

# 主函数
main() {
    local env=${1:-prod}
    local action=${2:-start}
    
    log_info "AI智能清洗策略系统部署脚本"
    log_info "环境: $env, 操作: $action"
    
    case $action in
        "build")
            check_dependencies
            check_environment
            build_images
            ;;
        "start")
            check_dependencies
            check_environment
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "logs")
            show_logs $3
            ;;
        "status")
            show_status
            ;;
        "clean")
            clean_up
            ;;
        "backup")
            backup_data
            ;;
        "deploy")
            check_dependencies
            check_environment
            build_images
            start_services
            ;;
        *)
            echo "使用方法: $0 [环境] [操作]"
            echo "环境: dev, staging, prod (默认: prod)"
            echo "操作: build, start, stop, restart, logs, status, clean, backup, deploy (默认: start)"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
