# AI智能清洗策略系统 - 环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# 应用环境
ENV=production
DEBUG=false

# 后端配置
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8000
API_BASE_URL=http://localhost:8000

# 前端配置
FRONTEND_PORT=80
REACT_APP_API_URL=http://localhost:8000

# JWT配置
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# 数据库配置 (如果使用)
DATABASE_URL=sqlite:///./ai_traffic.db
# DATABASE_URL=postgresql://user:password@localhost/ai_traffic

# Redis配置 (可选)
REDIS_URL=redis://localhost:6379/0

# 文件存储路径
DATA_DIR=/app/data
MODEL_DIR=/app/models
LOG_DIR=/app/logs

# 上传文件限制
MAX_FILE_SIZE=100MB
ALLOWED_EXTENSIONS=txt,csv

# 模型训练配置
DEFAULT_LEARNING_RATE=0.001
DEFAULT_BATCH_SIZE=32
DEFAULT_EPOCHS=100
DEFAULT_SEQUENCE_LENGTH=10
DEFAULT_HIDDEN_SIZE=64
DEFAULT_NUM_LAYERS=2
DEFAULT_DROPOUT=0.2

# 系统监控
ENABLE_MONITORING=true
LOG_LEVEL=INFO

# 安全配置
CORS_ORIGINS=http://localhost:3000,http://localhost:80
ALLOWED_HOSTS=localhost,127.0.0.1

# GPU配置 (如果有GPU)
CUDA_VISIBLE_DEVICES=0
USE_GPU=false
