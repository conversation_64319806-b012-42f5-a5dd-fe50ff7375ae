# AI智能清洗策略系统 - Python依赖包
# 基于代码分析生成的依赖列表

# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 数据处理
pandas==2.1.3
numpy==1.24.3

# 机器学习和深度学习
torch==2.1.1
torchvision==0.16.1
torchaudio==2.1.1
scikit-learn==1.3.2
joblib==1.3.2

# 数据预处理
scipy==1.11.4

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# HTTP客户端
requests==2.31.0
httpx==0.25.2

# 系统监控
psutil==5.9.6

# GPU监控 (可选)
pynvml==11.5.0

# 文件处理
python-dateutil==2.8.2
openpyxl==3.1.2

# 日志和配置
python-dotenv==1.0.0

# 数据库 (如果需要)
sqlalchemy==2.0.23
alembic==1.12.1

# 异步支持
asyncio-mqtt==0.13.0

# 开发和测试工具
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0

# 生产环境
gunicorn==21.2.0
